import {
  Baloo2_400Regular,
  Baloo2_500Medium,
  Baloo2_600SemiBold,
  Baloo2_700Bold,
  Baloo2_800ExtraBold,
} from "@expo-google-fonts/baloo-2";
import {
  Imbue_400Regular,
  Imbue_500Medium,
  Imbue_600SemiBold,
  Imbue_700Bold,
  Imbue_800ExtraBold,
  Imbue_900Black,
} from "@expo-google-fonts/imbue";
import {
  DarkTheme,
  DefaultTheme,
  ThemeProvider,
} from "@react-navigation/native";
import { useFonts } from "expo-font";
import { Stack } from "expo-router";
import { StatusBar } from "expo-status-bar";

import { useColorScheme } from "@/hooks/useColorScheme";
import { GestureHandlerRootView } from "react-native-gesture-handler";

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    ImbueBold: Imbue_700Bold,
    ImbueRegular: Imbue_400Regular,
    ImbueMedium: Imbue_500Medium,
    ImbueSemiBold: Imbue_600SemiBold,
    ImbueExtraBold: Imbue_800ExtraBold,
    ImbueBlack: Imbue_900Black,
    Baloo2Regular: Baloo2_400Regular,
    Baloo2Medium: Baloo2_500Medium,
    Baloo2SemiBold: Baloo2_600SemiBold,
    Baloo2Bold: Baloo2_700Bold,
    Baloo2ExtraBold: Baloo2_800ExtraBold,
  });

  if (!loaded) {
    return null;
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <ThemeProvider value={colorScheme === "dark" ? DarkTheme : DefaultTheme}>
        <Stack>
          <Stack.Screen name="index" options={{ headerShown: false }} />
          <Stack.Screen name="+not-found" />
        </Stack>
        <StatusBar style="auto" />
      </ThemeProvider>
    </GestureHandlerRootView>
  );
}
