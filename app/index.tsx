import Background from "@/assets/images/home-bg.png";
import CarouselCard from "@/components/CarouselCard";
import { Card<PERSON>rossingLines, CardEmberAfterglow, CardFirstDate, CardLateNightTalk } from "@/partials";
import BottomSheet, { BottomSheetView } from '@gorhom/bottom-sheet';
import React, { useCallback, useRef } from 'react';
import {
  ImageBackground,
  StatusBar,
  StyleSheet,
  Text,
  View,
} from "react-native";

export default function HomeScreen() {
  const bottomSheetRef = useRef<BottomSheet>(null);

  // callbacks
  const handleSheetChanges = useCallback((index: number) => {
    console.log('handleSheetChanges', index);
  }, []);

  const data = [
    { id: 1, comp: CardFirstDate },
    { id: 2, comp: CardLateNightTalk },
    { id: 3, comp: CardEmberAfterglow },
    { id: 4, comp: CardCrossingLines },
  ];

  return (
    <>
      <StatusBar barStyle="light-content" />
      <ImageBackground source={Background} style={styles.container}>
        {/* <View>
          <Text>Fire</Text>
        </View> */}
        <View style={styles.cardContainer}>
          <CarouselCard data={data} />
        </View>
        <View>
          <Text>Navigation</Text>
        </View>
      </ImageBackground>
        <BottomSheet
          ref={bottomSheetRef}
          onChange={handleSheetChanges}
          snapPoints={['25%', '50%', '90%']}
          index={-1}
        >
          <BottomSheetView style={styles.contentContainer}>
            <Text>Awesome 🎉</Text>
          </BottomSheetView>
        </BottomSheet>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#0B1713",
  },
  cardContainer: {
    flex: 1,
  },
  contentContainer: {
    flex: 1,
    padding: 36,
    alignItems: 'center',
  },
});
