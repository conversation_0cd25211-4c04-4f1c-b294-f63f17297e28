import Cover from "@/assets/images/bg-first-date.png";
import { Image } from "expo-image";
import { LinearGradient } from "expo-linear-gradient";
import { StyleSheet, Text, View } from "react-native";

export const CardFirstDate = () => {
  return (
    <LinearGradient
      colors={[
        "rgba(255, 172, 78, 1)",
        "rgba(153, 48, 116, 1)",
        "rgba(64, 0, 64, 1)",
        "rgba(41, 5, 72, 1)",
      ]}
      locations={[0, 0.14, 0.35, 0.55]}
      style={styles.container}
    >
      <View>
        <Image source={Cover} contentFit="cover" style={styles.image} />
        <Text style={styles.title}>First date</Text>
        <View style={styles.descriptionContainer}>
          <Text style={styles.description}>Skip the small talk.</Text>
          <Text style={styles.description}>Start something real.</Text>
        </View>
      </View>
      <View style={styles.priceContainer}>
        <Text style={styles.price}>$9.9/ 4h</Text>
        <Text style={styles.freeTrial}>Free trial</Text>
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    aspectRatio: 7 / 10,
    borderWidth: 1,
    borderColor: "#FFC48D",
    borderRadius: 16,
    padding: 16,
    alignItems: "center",
    justifyContent: "center",
    gap: 28,
  },
  imageContainer: {
    aspectRatio: 14 / 10,
    width: 100,
  },
  title: {
    fontSize: 40,
    fontFamily: "ImbueBold",
    color: "rgba(255, 255, 255, 0.85)",
    textTransform: "uppercase",
    textAlign: "center",
  },
  descriptionContainer: {
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    fontFamily: "Baloo2Regular",
    color: "rgba(255, 255, 255, 0.85)",
    opacity: 0.7,
    textAlign: "center",
  },
  image: {
    aspectRatio: 14 / 10,
    width: "90%",
    marginLeft: 16,
  },
  priceContainer: {
    gap: 12,
  },
  price: {
    fontSize: 14,
    fontFamily: "Baloo2Regular",
    textAlign: "center",
    color: "rgba(255, 255, 255, 0.85)",
  },
  freeTrial: {
    fontSize: 24,
    fontFamily: "ImbueMedium",
    textAlign: "center",
    color: "rgba(255, 255, 255, 0.85)",
  },
});
