import Cover from "@/assets/images/bg-late-night-talk.png";
import { Image } from "expo-image";
import { LinearGradient } from "expo-linear-gradient";
import { StyleSheet, Text, View } from "react-native";

export const CardLateNightTalk = () => {
  return (
    <LinearGradient
      colors={[
        "rgba(251, 192, 232, 1)",
        "rgba(168, 76, 235, 1)",
        "rgba(112, 59, 216, 1)",
        "rgba(72, 4, 206, 1)",
      ]}
      locations={[0, 0.14, 0.3, 0.45]}
      style={styles.container}
    >
      <View>
        <Image source={Cover} contentFit="cover" style={styles.image} />
        <Text style={styles.title}>LATE NIGHT TALK</Text>
        <View style={styles.descriptionContainer}>
          <Text style={styles.description}>The deeper it gets,</Text>
          <Text style={styles.description}>the later it gets.</Text>
        </View>
      </View>
      <View style={styles.priceContainer}>
        <Text style={styles.price}>$9.9/ 4h</Text>
        <Text style={styles.freeTrial}>Free trial</Text>
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    aspectRatio: 7 / 10,
    borderWidth: 1,
    borderColor: "#F1B2E8",
    borderRadius: 16,
    padding: 16,
    alignItems: "center",
    justifyContent: "center",
    gap: 28,
  },
  imageContainer: {
    aspectRatio: 14 / 10,
    width: 100,
  },
  title: {
    fontSize: 40,
    fontFamily: "ImbueBold",
    color: "rgba(255, 255, 255, 0.85)",
    textTransform: "uppercase",
    textAlign: "center",
  },
  descriptionContainer: {
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    fontFamily: "Baloo2Regular",
    color: "rgba(255, 255, 255, 0.85)",
    opacity: 0.7,
    textAlign: "center",
  },
  image: {
    aspectRatio: 14 / 10,
    width: "90%",
    marginLeft: 16,
  },
  priceContainer: {
    gap: 12,
  },
  price: {
    fontSize: 14,
    fontFamily: "Baloo2Regular",
    textAlign: "center",
    color: "rgba(255, 255, 255, 0.85)",
  },
  freeTrial: {
    fontSize: 24,
    fontFamily: "ImbueMedium",
    textAlign: "center",
    color: "rgba(255, 255, 255, 0.85)",
  },
});
