import Cover from "@/assets/images/bg-crossing-lines.png";
import { Image } from "expo-image";
import { LinearGradient } from "expo-linear-gradient";
import { StyleSheet, Text, View } from "react-native";

export const CardCrossingLines = () => {
  return (
    <LinearGradient
      colors={[
        "rgba(195, 102, 138, 1)",
        "rgba(162, 218, 253, 1)",
        "rgba(35, 172, 250, 1)",
        "rgba(15, 19, 129, 1)",
      ]}
      locations={[0, 0.07, 0.17, 0.45]}
      style={styles.container}
    >
      <View>
        <Image source={Cover} contentFit="cover" style={styles.image} />
        <View>
          <Text style={styles.title}>Crossing Lines</Text>
          <View style={styles.descriptionContainer}>
            <Text style={styles.description}>
              When friendship starts feeling like more.
            </Text>
          </View>
        </View>
      </View>
      <View style={styles.priceContainer}>
        <Text style={styles.price}>$9.9/ 4h</Text>
        <Text style={styles.freeTrial}>Free trial</Text>
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    aspectRatio: 7 / 10,
    borderWidth: 1,
    borderColor: "#0F1381",
    borderRadius: 16,
    padding: 16,
    alignItems: "center",
    justifyContent: "space-between",
    gap: 28,
  },
  imageContainer: {
    aspectRatio: 14 / 10,
    width: 100,
  },
  title: {
    fontSize: 40,
    fontFamily: "ImbueBold",
    color: "rgba(255, 255, 255, 0.85)",
    textTransform: "uppercase",
    textAlign: "center",
  },
  descriptionContainer: {
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    fontFamily: "Baloo2Regular",
    color: "rgba(255, 255, 255, 0.85)",
    opacity: 0.7,
    textAlign: "center",
  },
  image: {
    aspectRatio: 14 / 10,
    width: "90%",
    marginLeft: 16,
  },
  priceContainer: {
    gap: 12,
  },
  price: {
    fontSize: 14,
    fontFamily: "Baloo2Regular",
    textAlign: "center",
    color: "rgba(255, 255, 255, 0.85)",
  },
  freeTrial: {
    fontSize: 24,
    fontFamily: "ImbueMedium",
    textAlign: "center",
    color: "rgba(255, 255, 255, 0.85)",
  },
});
