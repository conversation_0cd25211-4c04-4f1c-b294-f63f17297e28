{"expo": {"name": "love-deck", "slug": "love-deck", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "<PERSON><PERSON>", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["expo-font", {"fonts": ["node_modules/@expo-google-fonts/imbue/fonts/Imbue-400Regular.ttf", "node_modules/@expo-google-fonts/imbue/fonts/Imbue-500Medium.ttf", "node_modules/@expo-google-fonts/imbue/fonts/Imbue-600SemiBold.ttf", "node_modules/@expo-google-fonts/imbue/fonts/Imbue-700Bold.ttf", "node_modules/@expo-google-fonts/imbue/fonts/Imbue-800ExtraBold.ttf", "node_modules/@expo-google-fonts/imbue/fonts/Imbue-900Black.ttf"]}]], "experiments": {"typedRoutes": true}}}