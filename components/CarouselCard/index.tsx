import * as React from "react";
import { Dimensions, View } from "react-native";
import Animated, { interpolate, useSharedValue } from "react-native-reanimated";
import Carousel, { TAnimationStyle } from "react-native-reanimated-carousel";

interface RenderItemProps {
  item: any;
  index: number;
  animationValue: any;
}

const renderItem = () => {
  // eslint-disable-next-line react/display-name
  return ({ item, index, animationValue }: RenderItemProps) => {

    const Component = item.comp;

    return (
      <Animated.View
        key={index}
        style={[
          {
            width: "100%",
            height: "100%",
            borderRadius: 20,
            padding: 20,
            justifyContent: "space-between",
            alignItems: "center",
            shadowColor: "#000",
            shadowOffset: {
              width: 0,
              height: 10,
            },
            shadowOpacity: 0.3,
            shadowRadius: 20,
            elevation: 15,
          },
        ]}
      >
        <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
          <Component />
        </View>
      </Animated.View>
    );
  };
};

  const { width: screenWidth } = Dimensions.get('window');


function TiltedCarousel({
  data,
} : {
  data: any[];
}) {
  const progress = useSharedValue<number>(0);

  const onProgressChange = (offsetProgress: number, absoluteProgress: number) => {
    progress.value = offsetProgress;
  };

  
  const animationStyle: TAnimationStyle = React.useCallback((value: number) => {
    "worklet";
 
	  const zIndex = Math.round(interpolate(value, [-1, 0, 1], [10, 20, 30]));
		const rotateZ = `${Math.round(interpolate(value, [-1, 0, 1], [-8, 0, 8]))}deg`;
		const translateX = Math.round(interpolate(value, [-1, 0, 1], [-screenWidth*0.9, 0, screenWidth*0.9]));
 
		return {
			transform: [{ translateX }, { rotateZ }] as const,
			zIndex,
		};
	}, [screenWidth]);

  return (
    <View style={{ 
      flex: 1, 
      backgroundColor: "#0F172A",
      justifyContent: "center",
      alignItems: "center" 
    }}>
      <Carousel
        // autoPlay
        autoPlayInterval={4000}
        data={data}
        height={400}
        width={screenWidth * 0.8}
        loop={true}
        pagingEnabled={true}
        snapEnabled={true}
        style={{
          width: screenWidth,
					justifyContent: "center",
					alignItems: "center",
        }}
        mode="parallax"
        modeConfig={{
          parallaxScrollingScale: 0.85,
          parallaxScrollingOffset: 80,
        }}
        onProgressChange={onProgressChange}
        renderItem={renderItem()}
        customAnimation={animationStyle}
      />
    </View>
  );
}

export default TiltedCarousel;